package com.phad.chatapp.viewmodels

import android.app.Application
import android.graphics.Bitmap
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.phad.chatapp.models.AttendanceEvent
import com.phad.chatapp.models.AttendanceSession
import com.phad.chatapp.models.AttendeeRecord
import com.phad.chatapp.models.QRAttendanceData
import com.phad.chatapp.repositories.AttendanceQRRepository
import com.phad.chatapp.services.QRAttendanceService
import com.phad.chatapp.services.QRValidationResult
import com.phad.chatapp.utils.QRAttendanceDebugUtils
import com.phad.chatapp.utils.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.Job

/**
 * ViewModel for managing QR-based attendance system
 * Follows MVVM pattern established in the app
 */
class QRAttendanceViewModel(private val application: Application) : ViewModel() {
    private val TAG = "QRAttendanceViewModel"
    
    // Dependencies
    private val repository = AttendanceQRRepository()
    private val qrService = QRAttendanceService()
    private val sessionManager = SessionManager(application)
    
    // UI State for Admin (Take Attendance)
    private val _adminUiState = MutableStateFlow(AdminQRUiState())
    val adminUiState: StateFlow<AdminQRUiState> = _adminUiState.asStateFlow()
    
    // UI State for Student (Give Attendance)
    private val _studentUiState = MutableStateFlow(StudentQRUiState())
    val studentUiState: StateFlow<StudentQRUiState> = _studentUiState.asStateFlow()
    
    // Current QR generation job
    private var qrGenerationJob: Job? = null
    
    // Current session listener job
    private var sessionListenerJob: Job? = null
    
    init {
        Log.d(TAG, "QRAttendanceViewModel initialized")
        loadUserInfo()
    }
    
    /**
     * Load current user information
     */
    private fun loadUserInfo() {
        val userType = sessionManager.fetchUserType()
        val userId = sessionManager.fetchUserId()
        val userName = sessionManager.fetchUserName()

        Log.d(TAG, "User info - Type: '$userType', ID: '$userId', Name: '$userName'")

        // Check admin status with detailed logging
        val isAdmin = userType == "Admin1" || userType == "Admin2" || userType == "Admin"
        Log.d(TAG, "Admin check: userType='$userType', isAdmin=$isAdmin")
        Log.d(TAG, "Admin1 check: ${userType == "Admin1"}, Admin2 check: ${userType == "Admin2"}, Admin check: ${userType == "Admin"}")

        _adminUiState.value = _adminUiState.value.copy(
            adminId = userId,
            adminName = userName,
            isAdmin = isAdmin
        )

        _studentUiState.value = _studentUiState.value.copy(
            studentId = userId,
            studentName = userName,
            isStudent = userType == "Student"
        )

        // Log final student UI state
        Log.d(TAG, "Final StudentQRUiState: studentId='${_studentUiState.value.studentId}', studentName='${_studentUiState.value.studentName}', isStudent=${_studentUiState.value.isStudent}")

        // Log final UI state
        Log.d(TAG, "Final AdminQRUiState: isAdmin=${_adminUiState.value.isAdmin}, isSessionActive=${_adminUiState.value.isSessionActive}")
    }
    
    /**
     * Load available events for attendance
     */
    fun loadAvailableEvents() {
        viewModelScope.launch {
            try {
                _adminUiState.value = _adminUiState.value.copy(isLoading = true)
                
                val result = repository.getAvailableEvents()
                if (result.isSuccess) {
                    val events = result.getOrNull() ?: emptyList()
                    _adminUiState.value = _adminUiState.value.copy(
                        availableEvents = events,
                        isLoading = false
                    )
                    Log.d(TAG, "Loaded ${events.size} available events")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "Failed to load events"
                    _adminUiState.value = _adminUiState.value.copy(
                        isLoading = false,
                        errorMessage = error
                    )
                    Log.e(TAG, "Error loading events: $error")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception loading events", e)
                _adminUiState.value = _adminUiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message ?: "Unknown error"
                )
            }
        }
    }

    /**
     * Create a new attendance event
     */
    fun createAttendanceEvent(name: String, description: String, eventDate: java.util.Date, openingTime: java.util.Date, closingTime: java.util.Date) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Creating attendance event: $name")
                _adminUiState.value = _adminUiState.value.copy(
                    isCreatingEvent = true,
                    errorMessage = null
                )

                // Generate document ID using the new format
                val documentId = com.phad.chatapp.utils.AttendanceEventUtils.generateDocumentId(eventDate, name)

                // Create new date/time format data
                val (dateString, timeRangeString) = com.phad.chatapp.utils.AttendanceEventUtils.createNewFormatEventTimeData(eventDate, openingTime, closingTime)

                Log.d(TAG, "Creating event with:")
                Log.d(TAG, "  Date string: $dateString")
                Log.d(TAG, "  Time range: $timeRangeString")

                val event = AttendanceEvent(
                    id = documentId,
                    eventDate = dateString,
                    eventTime = timeRangeString,
                    description = description.trim(),
                    createdBy = _adminUiState.value.adminId,
                    creatorName = _adminUiState.value.adminName,
                    createdAt = com.google.firebase.Timestamp.now(),
                    totalMarked = 0,
                    attendees = emptyList(),
                    closedAt = null
                )

                val result = repository.createAttendanceEvent(event)
                result.fold(
                    onSuccess = { eventId ->
                        Log.d(TAG, "Event created successfully with ID: $eventId")
                        _adminUiState.value = _adminUiState.value.copy(
                            isCreatingEvent = false,
                            showCreateEventDialog = false,
                            createEventSuccess = true,
                            errorMessage = null
                        )
                        // Refresh events list to show the newly created event
                        loadAvailableEvents()
                    },
                    onFailure = { error ->
                        Log.e(TAG, "Error creating event", error)
                        _adminUiState.value = _adminUiState.value.copy(
                            isCreatingEvent = false,
                            errorMessage = "Failed to create event: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error creating event", e)
                _adminUiState.value = _adminUiState.value.copy(
                    isCreatingEvent = false,
                    errorMessage = "Error creating event: ${e.message}"
                )
            }
        }
    }

    /**
     * Show create event dialog
     */
    fun showCreateEventDialog() {
        _adminUiState.value = _adminUiState.value.copy(
            showCreateEventDialog = true,
            createEventSuccess = false,
            errorMessage = null
        )
    }

    /**
     * Hide create event dialog
     */
    fun hideCreateEventDialog() {
        _adminUiState.value = _adminUiState.value.copy(
            showCreateEventDialog = false,
            createEventSuccess = false,
            errorMessage = null
        )
    }

    /**
     * Clear create event success state
     */
    fun clearCreateEventSuccess() {
        _adminUiState.value = _adminUiState.value.copy(
            createEventSuccess = false
        )
    }

    /**
     * Force refresh user info and UI state (for debugging)
     */
    fun refreshUserInfo() {
        Log.d(TAG, "Force refreshing user info...")
        loadUserInfo()
    }

    /**
     * Start attendance session for selected event (now simplified for consolidated schema)
     */
    fun startAttendanceSession(event: AttendanceEvent) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting attendance session for event: ${event.getEventName()}")

                _adminUiState.value = _adminUiState.value.copy(isLoading = true)

                // In the consolidated schema, we don't create separate session documents
                // The event itself serves as the "session"
                val sessionId = event.id // Use event ID as session ID

                _adminUiState.value = _adminUiState.value.copy(
                    selectedEvent = event,
                    isSessionActive = true,
                    isLoading = false
                )

                // Register session for security validation
                qrService.registerSession(sessionId, _adminUiState.value.adminId, event.id)

                // Start QR code generation
                startQRGeneration(sessionId, event.id)

                // Start listening to event updates (instead of session updates)
                startEventListener(event.id)

                Log.d(TAG, "Attendance session started successfully for consolidated event")
            } catch (e: Exception) {
                Log.e(TAG, "Exception starting session", e)
                _adminUiState.value = _adminUiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message ?: "Unknown error"
                )
            }
        }
    }
    
    /**
     * Start dynamic QR code generation
     */
    private fun startQRGeneration(sessionId: String, eventId: String) {
        qrGenerationJob?.cancel()
        qrGenerationJob = viewModelScope.launch {
            try {
                qrService.generateDynamicQRCodes(
                    sessionId = sessionId,
                    eventId = eventId,
                    adminId = _adminUiState.value.adminId
                ).collectLatest { (qrData, bitmap) ->
                    _adminUiState.value = _adminUiState.value.copy(
                        currentQRCode = bitmap,
                        currentQRData = qrData,
                        qrRefreshCount = _adminUiState.value.qrRefreshCount + 1
                    )
                    
                    // Update repository with new QR ID
                    repository.updateSessionQRCode(sessionId, qrData.qrId)
                    
                    Log.d(TAG, "QR code updated - ID: ${qrData.qrId}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in QR generation", e)
                _adminUiState.value = _adminUiState.value.copy(
                    errorMessage = "QR generation failed: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Start listening to event updates for real-time attendance count
     */
    private fun startEventListener(eventId: String) {
        sessionListenerJob?.cancel()
        sessionListenerJob = viewModelScope.launch {
            try {
                repository.listenToAttendanceEvent(eventId).collectLatest { event ->
                    event?.let {
                        _adminUiState.value = _adminUiState.value.copy(
                            selectedEvent = it,
                            attendeeCount = it.totalMarked
                        )
                        Log.d(TAG, "Event updated - Attendees: ${it.totalMarked}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error listening to event updates", e)
            }
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    @Deprecated("Use startEventListener instead")
    private fun startSessionListener(sessionId: String) {
        startEventListener(sessionId)
    }
    
    /**
     * End current attendance session
     */
    fun endAttendanceSession() {
        viewModelScope.launch {
            try {
                val sessionId = _adminUiState.value.currentSession?.id
                if (sessionId != null) {
                    Log.d(TAG, "Ending attendance session: $sessionId")
                    
                    val result = repository.endAttendanceSession(sessionId)
                    if (result.isSuccess) {
                        // End session in security validator
                        qrService.endSession(sessionId)

                        // Stop QR generation and session listener
                        qrGenerationJob?.cancel()
                        sessionListenerJob?.cancel()
                        
                        _adminUiState.value = _adminUiState.value.copy(
                            isSessionActive = false,
                            currentQRCode = null,
                            currentQRData = null
                        )
                        
                        Log.d(TAG, "Attendance session ended successfully")
                    } else {
                        val error = result.exceptionOrNull()?.message ?: "Failed to end session"
                        _adminUiState.value = _adminUiState.value.copy(errorMessage = error)
                        Log.e(TAG, "Error ending session: $error")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception ending session", e)
                _adminUiState.value = _adminUiState.value.copy(
                    errorMessage = e.message ?: "Unknown error"
                )
            }
        }
    }
    
    /**
     * Process scanned QR code for student attendance
     */
    fun processScannedQR(qrText: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Processing scanned QR code for student: ${_studentUiState.value.studentId}")
                Log.d(TAG, "QR Text length: ${qrText.length}")
                Log.d(TAG, "QR Text (first 200 chars): ${qrText.take(200)}")

                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = true,
                    scanResult = null
                )

                val result = qrService.validateQRCode(qrText, _studentUiState.value.studentId)
                if (result.isSuccess) {
                    val validationResult = result.getOrNull()!!

                    Log.d(TAG, "QR validation result - Valid: ${validationResult.isValid}, Reason: ${validationResult.reason}")

                    if (validationResult.qrData != null) {
                        Log.d(TAG, "QR Data - SessionId: ${validationResult.qrData.sessionId}, EventId: ${validationResult.qrData.eventId}, AdminId: ${validationResult.qrData.adminId}")
                        Log.d(TAG, "QR Data - QrId: ${validationResult.qrData.qrId}, Timestamp: ${validationResult.qrData.timestamp}")
                    }

                    if (validationResult.isValid && validationResult.qrData != null) {
                        Log.d(TAG, "QR validation successful, marking attendance")
                        // Mark attendance
                        markStudentAttendance(validationResult.qrData)
                    } else {
                        _studentUiState.value = _studentUiState.value.copy(
                            isProcessing = false,
                            scanResult = ScanResult.Error(validationResult.reason)
                        )
                        Log.w(TAG, "QR validation failed: ${validationResult.reason}")
                    }
                } else {
                    val error = result.exceptionOrNull()?.message ?: "Validation failed"
                    _studentUiState.value = _studentUiState.value.copy(
                        isProcessing = false,
                        scanResult = ScanResult.Error(error)
                    )
                    Log.e(TAG, "Error validating QR: $error", result.exceptionOrNull())
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception processing QR", e)
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error(e.message ?: "Unknown error")
                )
            }
        }
    }
    
    /**
     * Mark student attendance in the session
     */
    private suspend fun markStudentAttendance(qrData: QRAttendanceData) {
        try {
            Log.d(TAG, "=== STARTING ATTENDANCE MARKING PROCESS ===")
            Log.d(TAG, "Student ID: ${_studentUiState.value.studentId}")
            Log.d(TAG, "Student Name: ${_studentUiState.value.studentName}")
            Log.d(TAG, "Session ID: ${qrData.sessionId}")
            Log.d(TAG, "Event ID: ${qrData.eventId}")
            Log.d(TAG, "QR Code ID: ${qrData.qrId}")

            // Validate student data
            if (_studentUiState.value.studentId.isBlank()) {
                Log.e(TAG, "Student ID is blank!")
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error("Student ID not found")
                )
                return
            }

            // Get user information to populate NSS group
            val userInfo = repository.getUserByRollNumber(_studentUiState.value.studentId).getOrNull()
            val nssGroup = userInfo?.get("nss_group") as? String ?: "Unknown"

            val attendee = AttendeeRecord(
                rollNumber = _studentUiState.value.studentId,
                name = _studentUiState.value.studentName.ifBlank { "Unknown Student" },
                nssGroup = nssGroup,
                scanTimestamp = com.google.firebase.Timestamp.now(),
                scannedFrom = com.phad.chatapp.models.ScannedFromAdmin(
                    adminRollNumber = qrData.adminId,
                    adminName = _adminUiState.value.adminName
                ),
                qrCodeId = qrData.qrId,
                validationStatus = AttendeeRecord.STATUS_VALID
            )

            Log.d(TAG, "Created AttendeeRecord: rollNumber=${attendee.rollNumber}, studentName=${attendee.name}, qrCodeId=${attendee.qrCodeId}")
            Log.d(TAG, "AttendeeRecord validation: isDataValid=${attendee.isDataValid()}")

            Log.d(TAG, "Calling repository.addAttendeeToEvent...")
            val result = repository.addAttendeeToEvent(qrData.eventId, attendee)

            Log.d(TAG, "Repository result: isSuccess=${result.isSuccess}")
            if (result.isFailure) {
                Log.e(TAG, "Repository failure details:", result.exceptionOrNull())
            }

            if (result.isSuccess) {
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Success("Attendance marked successfully!")
                )
                Log.d(TAG, "✅ Attendance marked successfully for ${_studentUiState.value.studentId}")
                Log.d(TAG, "=== ATTENDANCE MARKING COMPLETED SUCCESSFULLY ===")
            } else {
                val error = result.exceptionOrNull()?.message ?: "Failed to mark attendance"
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error(error)
                )
                Log.e(TAG, "❌ Error marking attendance: $error", result.exceptionOrNull())
                Log.e(TAG, "=== ATTENDANCE MARKING FAILED ===")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception marking attendance", e)
            _studentUiState.value = _studentUiState.value.copy(
                isProcessing = false,
                scanResult = ScanResult.Error(e.message ?: "Unknown error")
            )
        }
    }
    
    /**
     * Clear error messages
     */
    fun clearError() {
        _adminUiState.value = _adminUiState.value.copy(errorMessage = null)
        _studentUiState.value = _studentUiState.value.copy(scanResult = null)
    }

    /**
     * Test method to verify attendance marking works
     */
    fun testAttendanceMarking() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "=== TESTING ATTENDANCE MARKING ===")

                // Create a test attendee record
                val testAttendee = AttendeeRecord(
                    rollNumber = "TEST123",
                    name = "Test Student",
                    qrCodeId = "test_qr_${System.currentTimeMillis()}",
                    validationStatus = AttendeeRecord.STATUS_VALID
                )

                // Get the current session ID from admin UI state
                val currentSession = _adminUiState.value.currentSession
                if (currentSession == null) {
                    Log.e(TAG, "No active session for testing")
                    return@launch
                }

                Log.d(TAG, "Testing with session: ${currentSession.id}")

                val result = repository.addAttendeeToSession(currentSession.id, testAttendee)
                if (result.isSuccess) {
                    Log.d(TAG, "✅ Test attendance marking successful")
                } else {
                    Log.e(TAG, "❌ Test attendance marking failed: ${result.exceptionOrNull()?.message}")
                }

                Log.d(TAG, "=== TEST COMPLETED ===")
            } catch (e: Exception) {
                Log.e(TAG, "Exception during test", e)
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        qrGenerationJob?.cancel()
        sessionListenerJob?.cancel()
        Log.d(TAG, "QRAttendanceViewModel cleared")
    }

    /**
     * Debug method to test QR attendance flow
     */
    fun debugTestQRFlow() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting QR attendance debug test...")

                val testSessionId = "debug_session_${System.currentTimeMillis()}"
                val testEventId = "debug_event_${System.currentTimeMillis()}"
                val adminId = _adminUiState.value.adminId
                val studentId = _studentUiState.value.studentId

                val result = QRAttendanceDebugUtils.testQRFlow(
                    sessionId = testSessionId,
                    eventId = testEventId,
                    adminId = adminId,
                    studentId = studentId,
                    qrService = qrService
                )

                Log.d(TAG, "Debug test completed - Success: ${result.success}")
                if (!result.success) {
                    Log.e(TAG, "Debug test failed:\n${result.report}")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Debug test exception", e)
            }
        }
    }

    /**
     * Close an attendance event manually
     */
    fun closeEvent(event: AttendanceEvent) {
        viewModelScope.launch {
            try {
                val result = repository.closeAttendanceEvent(event.id)
                if (result.isSuccess) {
                    // Reload available events to reflect the change
                    loadAvailableEvents()
                    Log.d(TAG, "Event closed successfully: ${event.getEventName()}")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "Failed to close event"
                    _adminUiState.value = _adminUiState.value.copy(errorMessage = error)
                    Log.e(TAG, "Error closing event: $error")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error closing event", e)
                _adminUiState.value = _adminUiState.value.copy(
                    errorMessage = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
}

/**
 * UI State for Admin QR Attendance interface
 */
data class AdminQRUiState(
    val isLoading: Boolean = false,
    val adminId: String = "",
    val adminName: String = "",
    val isAdmin: Boolean = false,
    val availableEvents: List<AttendanceEvent> = emptyList(),
    val selectedEvent: AttendanceEvent? = null,
    val currentSession: AttendanceSession? = null,
    val isSessionActive: Boolean = false,
    val currentQRCode: Bitmap? = null,
    val currentQRData: QRAttendanceData? = null,
    val qrRefreshCount: Int = 0,
    val attendeeCount: Int = 0,
    val errorMessage: String? = null,
    // Event creation states
    val showCreateEventDialog: Boolean = false,
    val isCreatingEvent: Boolean = false,
    val createEventSuccess: Boolean = false
)

/**
 * UI State for Student QR Attendance interface
 */
data class StudentQRUiState(
    val isProcessing: Boolean = false,
    val studentId: String = "",
    val studentName: String = "",
    val isStudent: Boolean = false,
    val scanResult: ScanResult? = null,
    val isCameraPermissionGranted: Boolean = false
)

/**
 * Sealed class representing scan results
 */
sealed class ScanResult {
    data class Success(val message: String) : ScanResult()
    data class Error(val message: String) : ScanResult()
}
